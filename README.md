# CodeCops 🚔

**Open Source Professional Interview Assistant with Invisible Screen Overlay**

[![License](https://img.shields.io/badge/License-Apache%202.0-blue.svg)](https://opensource.org/licenses/Apache-2.0)
[![GitHub stars](https://img.shields.io/github/stars/Rehan018/codeCops2.0.svg)](https://github.com/Rehan018/codeCops2.0/stargazers)
[![GitHub issues](https://img.shields.io/github/issues/Rehan018/codeCops2.0.svg)](https://github.com/Rehan018/codeCops2.0/issues)
[![GitHub forks](https://img.shields.io/github/forks/Rehan018/codeCops2.0.svg)](https://github.com/Rehan018/codeCops2.0/network)
[![PRs Welcome](https://img.shields.io/badge/PRs-welcome-brightgreen.svg)](http://makeapullrequest.com)

An AI-powered desktop tool that helps you excel in technical and professional interviews by providing intelligent, real-time assistance while remaining completely invisible to screen sharing and recording software.

### Demo
[Demo Video](./codeCops-video/codeCops.mp4)

## Perfect for Interviews
**Completely Stealth** - Invisible to Zoom, Teams, Meet, and all screen sharing tools
**Real-time AI Assistance** - Instant help with coding problems, system design, and interview questions
**Professional Skills** - Specialized modes for different interview types

### Supported Interview Skills
- **DSA (Data Structures & Algorithms)** - Complete solutions with complexity analysis
- **System Design** - Architecture patterns and scalability approaches  
- **Programming** - Multi-language coding assistance and best practices
- **Behavioral** - STAR method responses and professional scenarios
- **Sales** - Frameworks, objection handling, and closing techniques
- **Negotiation** - Strategic approaches and persuasion tactics
- **Presentation** - Structure, delivery tips, and visual design
- **DevOps** - Infrastructure, CI/CD, and deployment strategies
- **Data Science** - Analytics, ML approaches, and statistical methods

## 🚀 Quick Start

### Installation
```bash
git clone https://github.com/Rehan018/codeCops2.0.git
cd codeCops2.0
brew install tesseract
brew install sox
npm install
npm start
```

### Build Distributable App

#### Step-by-Step Build Process
1. **Clone and Setup** (first time only):
   ```bash
   git clone https://github.com/Rehan018/codeCops2.0.git
   cd codeCops2.0
   npm install
   ```

2. **Create Your Build**:
   ```bash
   # For your current platform (recommended)
   npm run build
   
   # Or specific platforms
   npm run build:mac      # macOS (.dmg + .zip)
   npm run build:win      # Windows (.exe installer + portable)
   npm run build:linux    # Linux (.AppImage + .deb)
   npm run build:all      # All platforms
   ```

3. **Find Your App**: Built files appear in `dist/` folder

#### Build Commands Reference
```bash
# Basic builds
npm run build          # Current platform
npm run build:mac      # macOS (.dmg + .zip)
npm run build:win      # Windows (.exe installer + portable)
npm run build:linux    # Linux (.AppImage + .deb)
npm run build:all      # All platforms

# Development & testing
npm run pack           # Quick build for testing (no compression)
npm run clean          # Clean dist/ folder
npm run rebuild        # Clean + build current platform
npm run release        # Clean + build all platforms
```

**Built apps will be in the `dist/` folder:**
- **macOS**: `CodeCops-1.0.0.dmg` (installer) or `CodeCops-1.0.0-mac.zip` (portable)
- **Windows**: `CodeCops Setup 1.0.0.exe` (installer) or `CodeCops 1.0.0.exe` (portable)
- **Linux**: `CodeCops-1.0.0.AppImage` (portable) or `CodeCops_1.0.0_amd64.deb` (installer)

### Installing Built Apps
- **macOS**: Double-click `.dmg` file → Drag to Applications folder
- **Windows**: Run `.exe` installer or double-click portable version
- **Linux**: Make `.AppImage` executable (`chmod +x`) and run, or install `.deb` with `dpkg`

**Clean Build Process:**
```bash
rm -rf node_modules dist
npm install
npm run build
```

### Essential Setup
1. **Azure Speech** (for voice commands)
   - Get free key from [Azure Portal](https://azure.microsoft.com/en-us/free/students)
   - Add to `.env`: `AZURE_SPEECH_KEY=your_key`

2. **Google Gemini AI** (for intelligent responses)
   - Get API key from [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Configure in app: Press `Alt+G`

##  📢 🎓 Students gets $100 free credits Azure, and Free Speech To Text for 5 hours of audio

### Environment File
Create `.env`:
```bash
AZURE_SPEECH_KEY=your_azure_speech_key
AZURE_SPEECH_REGION=your_region
GEMINI_API_KEY=your_gemini_api_key
```

## ⌨️ Essential Shortcuts

### Core Functions
| Shortcut | Action |
|----------|--------|
| `Cmd + Shift + S` | Screenshot + AI Analysis |
| `Alt/Option + R` | Voice Recording Toggle |
| `Cmd + Shift + \` | Show/Hide All Windows |
| `Alt + A` | Toggle Interactive Mode |

### Navigation
| Shortcut | Action |
|----------|--------|
| `Cmd + Shift + C` | Chat Window |
| `Cmd + Arrow Up/Down` | Skills Selection (only if Interactive mode is on) |
| `Cmd + ,` | Settings |

### Session Management
| Shortcut | Action |
|----------|--------|
| `Cmd+Shift+\` | Clear Session Memory |

### Important Interaction Usage Tip 
* Enable **Interaction Mode** to scroll, click, or select inside windows.
* Use `Cmd+Up/Down` (in Interaction Mode) to switch skills quickly.
* Click thorugh screen works only when interaction mode is disabled
* In **Stealth Mode**, windows are invisible to screen share & mouse.

## 🔧 Key Features

### Stealth Technology
- **Invisible to Screen Sharing** - Completely hidden from Zoom, Teams, Meet
- **Process Disguise** - Appears as "Terminal" in system monitors
- **Click-through Mode** - Windows become transparent to mouse clicks
- **No Screen Recording Detection** - Undetectable by recording software

### AI-Powered Analysis
- **Screenshot OCR** - Extract and analyze text from any screen content
- **Voice Commands** - Speak questions and get instant AI responses
- **Context-Aware** - Remembers conversation history for better responses
- **Multi-Format Output** - Clean text and code blocks with syntax highlighting

### Interview-Specific Intelligence
- **Problem Recognition** - Automatically detects interview question types
- **Step-by-Step Solutions** - Detailed explanations with best practices
- **Code Examples** - Multi-language implementations with optimizations

## 💡 Pro Tips

### During Technical Interviews
1. **Position Windows**: Place CodeCops windows in screen corners before sharing
2. **Use Voice Mode**: Whisper questions during "thinking time"
3. **Screenshot Problems**: Capture coding challenges for instant solutions
4. **Check Solutions**: Verify your approach with AI before implementing

### For System Design
1. **Capture Requirements**: Screenshot or voice record the problem statement
2. **Get Frameworks**: Ask for architectural patterns and trade-offs
3. **Verify Scalability**: Double-check your design decisions

### Behavioral Questions
1. **STAR Method**: Get structured response frameworks
2. **Industry Examples**: Request relevant scenarios for your field
3. **Follow-up Prep**: Prepare for common follow-up questions

## Important Technical Requirements (MUST INSTALL Before Running)
- **Node.js** 16+
- **Tesseract OCR** (`brew install tesseract`)
- **Audio Tool** (`brew install sox`)
- **Azure Speech Services** (Free tier available)
- **Google Gemini API** (Free quota included)

## 🚀 Advanced Usage

### Session Memory
The app remembers your interview context across multiple questions:

## 🤝 Contributing

**Help make CodeCops the ultimate open-source interview companion!**

### 🌟 Ways to Contribute

#### 🔧 Development
- **New Interview Skills** - Add specialized domains (Finance, Marketing, Legal, etc.)
- **Language Support** - Expand beyond English for global users
- **Platform Extensions** - Improve Windows and Linux compatibility
- **LLM Improvements** - Add support for multiple AI models (OpenAI, Claude, etc.)
- **UI/UX Improvements** - Enhanced interface and user experience
- **Performance Optimization** - Memory usage, startup time, response speed

#### 📚 Documentation
- **Tutorials** - Step-by-step guides for different use cases
- **API Documentation** - Document internal APIs and architecture
- **Translations** - Translate README and docs to other languages
- **Video Guides** - Create setup and usage tutorials

#### 🐛 Testing & Quality
- **Bug Reports** - Report issues with detailed reproduction steps
- **Feature Requests** - Suggest new features with use cases
- **Code Reviews** - Review pull requests and provide feedback
- **Testing** - Test on different platforms and configurations

### 📋 Contribution Guidelines

#### Before Contributing
1. **Check existing issues** - Avoid duplicate work
2. **Read our [Code of Conduct](CODE_OF_CONDUCT.md)**
3. **Review [Development Setup](DEVELOPMENT.md)**

#### Pull Request Process
1. **Fork** the repository
2. **Create feature branch**: `git checkout -b feature/amazing-feature`
3. **Make changes** with clear, descriptive commits
4. **Add tests** for new functionality
5. **Update documentation** as needed
6. **Submit pull request** with detailed description

#### Code Standards
- **JavaScript**: Follow ESLint configuration
- **Commits**: Use conventional commit format
- **Documentation**: Update README for user-facing changes
- **Testing**: Maintain test coverage above 80%

### 🏆 Recognition

Contributors are recognized in:
- **README Contributors Section**
- **Release Notes**
- **GitHub Discussions**
- **Social Media Shoutouts**

### 💰 Sponsorship

#### Support CodeCops Development

**Individual Sponsors**
- ☕ **Coffee Tier ($5/month)** - Buy the team coffee
- 🍕 **Pizza Tier ($15/month)** - Fuel late-night coding sessions
- 🚀 **Rocket Tier ($50/month)** - Accelerate feature development
- 💎 **Diamond Tier ($100/month)** - Priority feature requests

**Corporate Sponsors**
- 🏢 **Bronze ($500/month)** - Logo in README
- 🥈 **Silver ($1000/month)** - Logo in app + website
- 🥇 **Gold ($2500/month)** - Custom integrations + priority support
- 💼 **Enterprise ($5000/month)** - Dedicated support + custom features

**Sponsor Benefits:**
- **Priority Support** - Faster response to issues
- **Feature Requests** - Influence development roadmap
- **Recognition** - Logo placement and social media mentions
- **Early Access** - Beta features and releases

[**Become a Sponsor**](https://github.com/sponsors/Rehan018) | [**Corporate Sponsorship**](mailto:<EMAIL>)

### 📞 Community

- **Discord**: [Join our community](https://discord.gg/codecops)
- **GitHub Discussions**: [Ask questions & share ideas](https://github.com/Rehan018/codeCops2.0/discussions)
- **Twitter**: [@CodeCopsApp](https://twitter.com/codecopsapp)
- **Email**: [<EMAIL>](mailto:<EMAIL>)

### 📜 License & Legal

#### Open Source License
CodeCops is licensed under the **Apache License 2.0**. See [LICENSE](LICENSE) for details.

#### Ethical Use Policy
**CodeCops is designed as a learning and preparation tool, not for cheating.**

**Acceptable Use:**
✅ Interview preparation and practice
✅ Learning programming concepts
✅ Studying system design patterns
✅ Improving technical communication
✅ Accessibility assistance for disabilities

**Prohibited Use:**
❌ Cheating in actual interviews
❌ Academic dishonesty
❌ Violating company policies
❌ Misrepresenting your abilities

**Disclaimer:** Users are responsible for complying with their organization's policies and ethical standards.

#### Privacy Policy
- **No Data Collection** - CodeCops runs entirely locally
- **API Keys** - Stored locally, never transmitted to our servers
- **Screenshots** - Processed locally, never uploaded
- **Voice Data** - Sent only to your configured speech service

### 🎯 Roadmap

#### Version 2.0 (Q2 2025)
- [ ] Multi-language support (Spanish, French, German)
- [ ] OpenAI GPT-4 integration
- [ ] Advanced code analysis
- [ ] Interview simulation mode

#### Version 2.5 (Q3 2025)
- [ ] Mobile companion app
- [ ] Team collaboration features
- [ ] Advanced analytics
- [ ] Custom skill templates

#### Version 3.0 (Q4 2025)
- [ ] AI-powered interview coaching
- [ ] Real-time performance feedback
- [ ] Integration with job platforms
- [ ] Advanced stealth improvements

### 🏅 Contributors

Thanks to all our amazing contributors!

<!-- ALL-CONTRIBUTORS-LIST:START -->
<!-- This section is auto-generated, do not modify manually -->
<!-- ALL-CONTRIBUTORS-LIST:END -->

### 📊 Project Stats

![GitHub commit activity](https://img.shields.io/github/commit-activity/m/Rehan018/codeCops2.0)
![GitHub last commit](https://img.shields.io/github/last-commit/username/codecops)
![GitHub code size](https://img.shields.io/github/languages/code-size/username/codecops)

---

⭐ **Star this repo** if CodeCops helped you ace your interviews!
🐦 **Follow us** [@CodeCopsApp](https://twitter.com/codecopsapp) for updates!
💬 **Join our Discord** for community support and discussions!
