# Contributing to CodeCops 🚔

Thank you for your interest in contributing to CodeCops! This document provides guidelines and information for contributors.

## 🚀 Quick Start

1. **Fork** the repository
2. **Clone** your fork: `git clone https://github.com/Rehan018/codeCops2.0.git`
3. **Install** dependencies: `npm install`
4. **Create** a branch: `git checkout -b feature/your-feature-name`
5. **Make** your changes
6. **Test** your changes: `npm test`
7. **Submit** a pull request

## 🛠️ Development Setup

### Prerequisites
- Node.js 16+ 
- npm or yarn
- Git

### Local Development
```bash
# Clone the repository
git clone https://github.com/Rehan018/codeCops2.0.git
cd codeCops2.0

# Install dependencies
npm install

# Start development server
npm run dev

# Run tests
npm test

# Build for production
npm run build
```

### Project Structure
```
codecops/
├── src/                 # Source code
│   ├── core/           # Core functionality
│   ├── services/       # External service integrations
│   ├── ui/             # User interface components
│   └── managers/       # Application managers
├── prompts/            # AI prompt templates
├── assets/             # Static assets
├── tests/              # Test files
└── docs/               # Documentation
```

## 📝 Contribution Types

### 🐛 Bug Reports
- Use the bug report template
- Include reproduction steps
- Provide system information
- Add screenshots if applicable

### ✨ Feature Requests
- Use the feature request template
- Explain the use case
- Provide mockups if applicable
- Consider implementation complexity

### 🔧 Code Contributions
- Follow coding standards
- Add tests for new features
- Update documentation
- Keep commits atomic and descriptive

### 📚 Documentation
- Fix typos and grammar
- Add examples and tutorials
- Improve API documentation
- Translate to other languages

## 🎯 Coding Standards

### JavaScript Style
- Use ESLint configuration
- Prefer `const` over `let`
- Use meaningful variable names
- Add JSDoc comments for functions

### Commit Messages
Follow [Conventional Commits](https://conventionalcommits.org/):
```
feat: add voice command support
fix: resolve screenshot capture issue
docs: update installation guide
test: add unit tests for OCR service
```

### Code Review Process
1. All code changes require review
2. Address reviewer feedback
3. Ensure CI passes
4. Squash commits before merge

## 🧪 Testing

### Running Tests
```bash
# Run all tests
npm test

# Run specific test file
npm test -- --grep "OCR Service"

# Run tests with coverage
npm run test:coverage
```

### Writing Tests
- Write unit tests for new functions
- Add integration tests for features
- Mock external dependencies
- Aim for 80%+ code coverage

## 📋 Pull Request Guidelines

### Before Submitting
- [ ] Code follows style guidelines
- [ ] Tests pass locally
- [ ] Documentation updated
- [ ] No merge conflicts
- [ ] Descriptive PR title and description

### PR Template
```markdown
## Description
Brief description of changes

## Type of Change
- [ ] Bug fix
- [ ] New feature
- [ ] Documentation update
- [ ] Performance improvement

## Testing
- [ ] Unit tests added/updated
- [ ] Manual testing completed
- [ ] No breaking changes

## Screenshots (if applicable)
Add screenshots here
```

## 🏷️ Issue Labels

- `bug` - Something isn't working
- `enhancement` - New feature or request
- `documentation` - Documentation improvements
- `good first issue` - Good for newcomers
- `help wanted` - Extra attention needed
- `priority:high` - High priority issue
- `priority:low` - Low priority issue

## 🌍 Internationalization

### Adding New Languages
1. Create language file in `src/locales/`
2. Add translations for all keys
3. Update language selector
4. Test with different locales

### Translation Guidelines
- Keep translations concise
- Maintain context and meaning
- Test UI layout with longer text
- Use native speakers for review

## 🔒 Security

### Reporting Security Issues
- **DO NOT** create public issues for security vulnerabilities
- Email <EMAIL> with details
- Include reproduction steps
- Allow time for fix before disclosure

### Security Best Practices
- Never commit API keys or secrets
- Validate all user inputs
- Use secure communication protocols
- Follow OWASP guidelines

## 📞 Getting Help

### Community Support
- **Discord**: [Join our community](https://discord.gg/codecops)
- **GitHub Discussions**: [Ask questions](https://github.com/username/codecops/discussions)
- **Email**: [<EMAIL>](mailto:<EMAIL>)

### Mentorship Program
New contributors can request mentorship:
- Comment on issues with `@mentor-needed`
- Join our Discord #mentorship channel
- Attend weekly contributor calls

## 🎉 Recognition

### Contributor Recognition
- Contributors added to README
- Mentioned in release notes
- Social media shoutouts
- Swag for significant contributions

### Contribution Levels
- **First-time contributor**: Welcome package
- **Regular contributor**: CodeCops stickers
- **Core contributor**: T-shirt and recognition
- **Maintainer**: Special privileges and responsibilities

## 📊 Project Metrics

We track these metrics to measure project health:
- Code coverage percentage
- Issue resolution time
- PR review time
- Community engagement
- Download/usage statistics

## 🗺️ Roadmap Participation

Contributors can influence the roadmap:
- Vote on feature priorities
- Propose new features
- Lead feature development
- Participate in architecture decisions

## ⚖️ License

By contributing to CodeCops, you agree that your contributions will be licensed under the Apache License 2.0.

---

**Thank you for contributing to CodeCops!** 🙏

Your contributions help make interview preparation accessible to everyone.