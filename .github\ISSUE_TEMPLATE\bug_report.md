---
name: Bug report
about: Create a report to help us improve CodeCops
title: '[BUG] '
labels: 'bug'
assignees: ''
---

## 🐛 Bug Description
A clear and concise description of what the bug is.

## 🔄 Steps to Reproduce
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

## ✅ Expected Behavior
A clear and concise description of what you expected to happen.

## ❌ Actual Behavior
A clear and concise description of what actually happened.

## 📸 Screenshots
If applicable, add screenshots to help explain your problem.

## 💻 Environment
- **OS**: [e.g. Windows 11, macOS 13.0, Ubuntu 22.04]
- **CodeCops Version**: [e.g. 1.0.0]
- **Node.js Version**: [e.g. 18.17.0]
- **Electron Version**: [e.g. 29.1.0]

## 🔧 Configuration
- **AI Service**: [e.g. Google Gemini, OpenAI]
- **Speech Service**: [e.g. Azure Speech, None]
- **OCR Service**: [e.g. Tesseract, Built-in]

## 📋 Additional Context
Add any other context about the problem here.

## 🚨 Error Logs
If applicable, paste any error messages or logs here:

```
Paste error logs here
```

## ✅ Checklist
- [ ] I have searched existing issues to avoid duplicates
- [ ] I have provided all requested information
- [ ] I can reproduce this bug consistently
- [ ] I have tested with the latest version