{"name": "codecops", "version": "1.0.0", "description": "Open Source Professional Interview Assistant with Invisible Screen Overlay", "main": "main.js", "scripts": {"start": "electron .", "dev": "electron . --no-sandbox --disable-gpu", "test-speech": "node test-azure-speech.js", "build": "electron-builder", "build:mac": "electron-builder --mac", "build:win": "electron-builder --win", "build:linux": "electron-builder --linux", "build:all": "electron-builder --mac --win --linux", "dist": "npm run build", "pack": "electron-builder --dir", "clean": "rmdir /s /q dist 2>nul || echo Clean completed", "rebuild": "npm run clean && npm run build", "release": "npm run clean && npm run build:all", "postinstall": "electron-builder install-app-deps"}, "author": {"name": "CodeCops Team", "email": "<EMAIL>"}, "license": "Apache-2.0", "homepage": "https://github.com/Rehan018/codeCops2.0", "repository": {"type": "git", "url": "https://github.com/Rehan018/codeCops2.0.git"}, "bugs": {"url": "https://github.com/Rehan018/codeCops2.0/issues"}, "keywords": ["interview", "assistant", "ai", "coding", "preparation", "stealth", "screen-sharing", "open-source"], "dependencies": {"@google/generative-ai": "^0.24.1", "dotenv": "^16.3.1", "markdown": "^0.5.0", "marked": "^15.0.12", "microsoft-cognitiveservices-speech-sdk": "^1.40.0", "node-record-lpcm16": "^1.0.1", "tesseract.js": "^6.0.1", "winston": "^3.17.0", "winston-daily-rotate-file": "^4.7.1"}, "devDependencies": {"electron": "^29.1.0", "electron-builder": "^24.13.3", "tailwindcss": "^4.1.11"}, "build": {"appId": "com.codecops.app", "productName": "CodeCops", "directories": {"output": "dist"}, "files": ["**/*", "!dist/**/*", "!*.md", "!.git/**/*", "!.env*"], "extraFiles": [{"from": "prompts", "to": "prompts"}], "asarUnpack": ["node_modules/**/*"], "mac": {"category": "public.app-category.utilities", "target": [{"target": "dmg", "arch": ["x64", "arm64"]}, {"target": "zip", "arch": ["x64", "arm64"]}], "icon": "assets/icons/app-icon.icns", "darkModeSupport": true, "hardenedRuntime": true, "gatekeeperAssess": false, "entitlements": "build/entitlements.mac.plist", "entitlementsInherit": "build/entitlements.mac.plist"}, "win": {"target": [{"target": "nsis", "arch": ["x64", "ia32"]}, {"target": "portable", "arch": ["x64", "ia32"]}], "icon": "assets/icons/app-icon.ico"}, "linux": {"target": [{"target": "AppImage", "arch": ["x64"]}, {"target": "deb", "arch": ["x64"]}], "icon": "assets/icons/app-icon.png", "category": "Utility"}, "nsis": {"oneClick": false, "allowToChangeInstallationDirectory": true, "createDesktopShortcut": true, "createStartMenuShortcut": true}, "dmg": {"title": "CodeCops Interview Assistant", "backgroundColor": "#000000", "window": {"width": 600, "height": 400}}}}