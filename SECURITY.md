# Security Policy

## Supported Versions

We actively support the following versions of CodeCops with security updates:

| Version | Supported          |
| ------- | ------------------ |
| 1.0.x   | :white_check_mark: |
| < 1.0   | :x:                |

## Reporting a Vulnerability

### How to Report

**Please do NOT report security vulnerabilities through public GitHub issues.**

Instead, please report security vulnerabilities to us via:

- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **Encrypted Email**: Use our [PGP key](https://codecops.dev/pgp-key.asc)
- **Private Disclosure**: GitHub Security Advisories (preferred)

### What to Include

Please include the following information in your report:

- **Description** of the vulnerability
- **Steps to reproduce** the issue
- **Potential impact** and attack scenarios
- **Suggested fix** (if you have one)
- **Your contact information** for follow-up

### Response Timeline

We take security seriously and will respond to security reports as follows:

- **Initial Response**: Within 24 hours
- **Status Update**: Within 72 hours
- **Fix Timeline**: Within 30 days for critical issues
- **Public Disclosure**: After fix is released and users have time to update

### Security Measures

CodeCops implements several security measures:

#### Data Privacy
- **Local Processing**: All data processed locally on your machine
- **No Data Collection**: We don't collect or store user data
- **API Keys**: Stored locally, never transmitted to our servers
- **Screenshots**: Processed locally using Tesseract OCR

#### Network Security
- **HTTPS Only**: All external API calls use HTTPS
- **Certificate Validation**: SSL certificates are properly validated
- **No Telemetry**: No usage data sent to external servers

#### Application Security
- **Code Signing**: Releases are signed with verified certificates
- **Dependency Scanning**: Regular security audits of dependencies
- **Input Validation**: All user inputs are validated and sanitized
- **Sandboxing**: Electron security best practices implemented

### Vulnerability Disclosure Policy

We follow responsible disclosure practices:

1. **Private Reporting**: Security issues reported privately first
2. **Investigation**: We investigate and develop fixes
3. **Coordinated Disclosure**: Public disclosure after fix is available
4. **Credit**: Security researchers credited (with permission)

### Security Best Practices for Users

#### Installation Security
- **Official Sources**: Download only from official GitHub releases
- **Verify Signatures**: Check code signatures on downloaded files
- **Keep Updated**: Install security updates promptly

#### Usage Security
- **API Keys**: Use dedicated API keys with minimal permissions
- **Network**: Use on trusted networks when possible
- **Permissions**: Review and understand app permissions

#### Configuration Security
- **Environment Files**: Keep `.env` files secure and private
- **Access Control**: Limit access to CodeCops configuration
- **Regular Updates**: Keep dependencies and system updated

### Known Security Considerations

#### Screen Sharing Detection
- CodeCops is designed to be invisible to screen sharing
- This feature should be used ethically and legally
- Users are responsible for compliance with policies

#### API Key Management
- API keys are stored in plain text locally
- Consider using environment variables or secure storage
- Rotate API keys regularly

#### Voice Data
- Voice recordings sent to configured speech services
- Review privacy policies of speech service providers
- Consider using local speech recognition when available

### Security Audits

We conduct regular security audits:

- **Code Reviews**: All code changes reviewed for security
- **Dependency Audits**: Regular scanning for vulnerable dependencies
- **Penetration Testing**: Periodic security testing
- **Third-party Audits**: External security assessments

### Compliance

CodeCops aims to comply with:

- **GDPR**: European data protection regulations
- **CCPA**: California consumer privacy act
- **SOC 2**: Security and availability standards
- **OWASP**: Web application security guidelines

### Security Contact

For security-related questions or concerns:

- **Email**: [<EMAIL>](mailto:<EMAIL>)
- **PGP Key**: [Download our public key](https://codecops.dev/pgp-key.asc)
- **Response Time**: Within 24 hours for security issues

### Hall of Fame

We recognize security researchers who help improve CodeCops security:

<!-- Security researchers will be listed here with their permission -->

---

**Thank you for helping keep CodeCops secure!** 🔒